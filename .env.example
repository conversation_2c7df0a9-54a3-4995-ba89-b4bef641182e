# ========== 必填项 (Required) ==========
# 币安 API (必须)
BINANCE_API_KEY="your_binance_api_key_here"
BINANCE_API_SECRET="your_binance_api_secret_here"


# ========== 策略核心配置 (Strategy Core Settings) ==========
# 要运行的交易对列表，用英文逗号分隔
# 例如: "BNB/USDT,ETH/USDT,BTC/USDT"
SYMBOLS="BNB/USDT,ETH/USDT"

# 按交易对设置初始参数 (JSON格式)
# 为每个交易对设置 "initial_base_price" 和 "initial_grid"
# 如果某个交易对在此处未定义，程序将自动使用当前市价作为基准价，并使用下面的全局INITIAL_GRID
INITIAL_PARAMS_JSON='{"BNB/USDT": {"initial_base_price": 600.0, "initial_grid": 2.0}, "ETH/USDT": {"initial_base_price": 3000.0, "initial_grid": 2.5}, "BTC/USDT": {"initial_base_price": 45000.0, "initial_grid": 1.5}}'

# 全局默认初始网格大小 (%)
# 仅当上面的INITIAL_PARAMS_JSON中未指定某个交易对的网格时使用
INITIAL_GRID=2.0

# 最小交易金额 (USDT)，请确保大于交易所对该交易对的最小名义价值要求 (通常是10 USDT)
MIN_TRADE_AMOUNT=20.0


# ========== 初始状态设置 (Initial State) ==========
# 初始本金 (USDT)，用于计算总盈亏和盈亏率。如果不知道，可以留空或设为0。
INITIAL_PRINCIPAL=1000.0


# ========== 可选配置 (Optional) ==========
# PushPlus 通知 Token，用于发送交易通知或警报
PUSHPLUS_TOKEN="your_pushplus_token_here"

# 是否启用自动申购/赎回币安活期理财的功能 (true/false)
# 对于使用子账户API的用户，或不希望使用理财功能的用户，请设置为 false
ENABLE_SAVINGS_FUNCTION=true

# Web UI 访问认证。如果留空，Web界面将无需密码即可访问。
WEB_USER="admin"
WEB_PASSWORD="YourSecurePasswordHere"

# 代理设置 (如果需要通过代理连接币安)
# 例如: HTTP_PROXY="http://127.0.0.1:7890"
HTTP_PROXY=


# ========== 高级策略配置 (Advanced Strategy Settings) ==========
# 网格参数 (JSON格式)
GRID_PARAMS_JSON='{"min": 1.0, "max": 4.0, "volatility_threshold": {"ranges": [{"range": [0, 0.10], "grid": 1.0}, {"range": [0.10, 0.20], "grid": 2.0}, {"range": [0.20, 0.30], "grid": 3.0}, {"range": [0.30, 0.40], "grid": 4.0}, {"range": [0.40, 999], "grid": 4.0}]}}'

# 连续网格调整参数 (JSON格式)
GRID_CONTINUOUS_PARAMS_JSON='{"base_grid": 2.5, "center_volatility": 0.25, "sensitivity_k": 10.0}'

# 动态时间间隔参数 (JSON格式)
DYNAMIC_INTERVAL_PARAMS_JSON='{"volatility_to_interval_hours": [{"range": [0, 0.10], "interval_hours": 1.0}, {"range": [0.10, 0.20], "interval_hours": 0.5}, {"range": [0.20, 0.30], "interval_hours": 0.25}, {"range": [0.30, 999], "interval_hours": 0.125}], "default_interval_hours": 1.0}'

# 是否启用成交量加权波动率计算 (true/false)
ENABLE_VOLUME_WEIGHTING=true

# ========== 高级配置 (Advanced - 通常无需修改) ==========
# 理财产品申购/赎回的精度 (JSON格式)
# 现在您可以按需添加更多币种的精度设置。'DEFAULT'用于未明确指定的币种。
SAVINGS_PRECISIONS='{"USDT": 2, "BNB": 6, "ETH": 5, "BTC": 8, "ADA": 4, "DOT": 4, "MATIC": 2, "LINK": 4, "UNI": 4, "DEFAULT": 8}'